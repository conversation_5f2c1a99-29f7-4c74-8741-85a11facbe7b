import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, SafeAreaView, ImageBackground, Dimensions, ActivityIndicator } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { validateOtp } from '../../utils/validators';
import authServices from '../../services/authServices';
// ZYNC 


// Constants
const { height } = Dimensions.get('window');

// Main OTP Verification Component
const OtpVerificationScreen: React.FC = () => {
  // Form data
  const [otp, setOtp] = useState('');
  
  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  
  // Error states
  const [otpError, setOtpError] = useState('');
  const [verificationError, setVerificationError] = useState('');
  
  // Navigation and context
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();

  // Get params from route (passed from RegisterScreen or LoginScreen)
  const email = (route.params as any)?.email || 'your email';
  const fromScreen = (route.params as any)?.fromScreen || 'register';
  const message = (route.params as any)?.message || 'Please verify your email with OTP.';

  // Event Handlers
  // Handles OTP input changes and clears errors
  const handleOtpChange = (text: string) => {
    // Remove any non-numeric characters and trim whitespace
    const cleanedText = text.replace(/[^0-9]/g, '').trim();
    setOtp(cleanedText);
    if (otpError) {
      setOtpError('');
    }
  };

  // Handles the OTP verification with validation
  const handleVerifyOtp = async () => {
    // Clear previous errors
    setOtpError('');
    setVerificationError('');

    // Validate OTP
    if (!otp) {
      setOtpError('Please enter the OTP code');
      return;
    }

    if (!validateOtp(otp)) {
      setOtpError('Please enter a valid 6-digit OTP code');
      return;
    }

    // Attempt verification
    setIsLoading(true);
    try {
      // Add 1.5 second delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      console.log('🔍 [OtpVerificationScreen] Sending OTP verification request:', { email, otp });
      const response = await authServices.verifyRegisterOtp({ email, otp });
      console.log('📋 [OtpVerificationScreen] OTP verification response:', response);

      if (response.success && response.data) {
        // Verification successful
        if (fromScreen === 'register') {
          // Navigate to login screen after successful registration verification
          setVerificationError('Email verified successfully! Please login to continue.');
          setTimeout(() => {
            (navigation as any).navigate('auth/login');
          }, 2000); // Show success message for 2 seconds then navigate
        } else {
          // Navigate to login screen after successful verification
          (navigation as any).navigate('auth/login');
        }
      } else {
        // Handle specific error messages from backend
        setVerificationError(response.error || 'Invalid OTP. Please try again.');
      }
    } catch (error: any) {
      console.error('OTP verification error:', error);
      setVerificationError('Network error. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handles resending OTP
  const handleResendOtp = async () => {
    setIsResending(true);
    setVerificationError('');
    try {
      // Add 1 second delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000));

      const response = await authServices.regenerateOtp(email);

      if (response.success && response.data) {
        setVerificationError('OTP resent to your email successfully.');
      } else {
        setVerificationError(response.error || 'Failed to resend OTP. Please try again.');
      }
    } catch (error: any) {
      console.error('Resend OTP error:', error);
      setVerificationError('Network error. Please check your connection and try again.');
    } finally {
      setIsResending(false);
    }
  };

  // Render Methods
  // Renders the OTP input field with validation error display
  const renderOtpField = () => (
    <View className="mb-6">
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-3 ${theme.colors.text} text-center text-xl ${otpError ? 'border-red-500' : ''}`}
        placeholder="000000"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={otp}
        onChangeText={handleOtpChange}
        keyboardType="numeric"
        maxLength={6}
        style={{ textAlignVertical: 'center' }}
      />
      {otpError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1 text-center">{otpError}</Text>
      ) : null}
    </View>
  );

  // Renders the verify button with loading state
  const renderVerifyButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleVerifyOtp}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Verifying...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Verify Account
        </Text>
      )}
    </TouchableOpacity>
  );

  // Renders the resend OTP section
  const renderResendSection = () => (
    <View className="flex-row justify-center mb-4">
      <Text className={`${theme.colors.textSecondary} mr-2`}>
        Didn't receive the code?
      </Text>
      <TouchableOpacity onPress={handleResendOtp} disabled={isResending}>
        <Text className={`font-medium ${isResending ? 'text-gray-400' : 'text-blue-500'}`}>
          {isResending ? 'Sending...' : 'Resend'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* Background Image Section */}
      <View style={{ height: height * 0.26 }}>
        <ImageBackground
          source={require('../../assets/images/auth/auth_bg.png')}
          style={{ flex: 1 }}
          resizeMode="cover"
        />
      </View>

      {/* OTP Verification Form Container */}
      <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
        {/* Welcome Header */}
        <View className="mb-4">
          <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
            Verify Account
          </Text>
          <Text className={`text-start ${theme.colors.textSecondary}`}>
            Enter the 6-digit code sent to {email}
          </Text>
        </View>

        {/* Form Fields */}
        {renderOtpField()}

        {/* Verification Message Display */}
        {verificationError ? (
          <View className={`mb-4 p-3 border rounded-lg ${
            verificationError.includes('successfully') || verificationError.includes('resent')
              ? 'bg-green-50 border-green-200'
              : 'bg-red-50 border-red-200'
          }`}>
            <Text className={`text-sm text-center ${
              verificationError.includes('successfully') || verificationError.includes('resent')
                ? 'text-green-600'
                : 'text-red-600'
            }`}>
              {verificationError}
            </Text>
          </View>
        ) : null}

        {/* Verify Button */}
        {renderVerifyButton()}

        {/* Resend OTP Section */}
        {renderResendSection()}

        {/* Back to Register Link */}
        <View className="flex-row justify-center">
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text className="text-blue-500 font-medium">
              Back to Register
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default OtpVerificationScreen;
