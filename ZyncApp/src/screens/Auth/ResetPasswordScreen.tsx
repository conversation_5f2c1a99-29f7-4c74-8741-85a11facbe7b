import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, SafeAreaView, ImageBackground, Dimensions, ActivityIndicator } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { validatePassword, validateConfirmPassword } from '../../utils/validators';
import Ionicons from 'react-native-vector-icons/Ionicons';
// ZYNC 

// Constants
const { height } = Dimensions.get('window');

// Main Reset Password Component
const ResetPasswordScreen: React.FC = () => {
  // Form data
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  
  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Error states
  const [newPasswordError, setNewPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [resetError, setResetError] = useState('');
  
  // Navigation and context
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();

  // Get email from route params (passed from OTP verification)
  const email = (route.params as any)?.email || 'your email';

  // Event Handlers
  // Handles new password input changes and clears errors
  const handleNewPasswordChange = (text: string) => {
    setNewPassword(text);
    if (newPasswordError) {
      setNewPasswordError('');
    }
  };

  // Handles confirm password input changes and clears errors
  const handleConfirmPasswordChange = (text: string) => {
    setConfirmPassword(text);
    if (confirmPasswordError) {
      setConfirmPasswordError('');
    }
  };

  // Handles the password reset with validation
  const handleResetPassword = async () => {
    // Clear previous errors
    setNewPasswordError('');
    setConfirmPasswordError('');
    setResetError('');

    // Validate new password
    if (!newPassword) {
      setNewPasswordError('Please enter a new password');
      return;
    }

    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      setNewPasswordError(passwordValidation.errors[0]); // Show first error
      return;
    }

    // Validate confirm password
    if (!confirmPassword) {
      setConfirmPasswordError('Please confirm your new password');
      return;
    }

    if (!validateConfirmPassword(newPassword, confirmPassword)) {
      setConfirmPasswordError('Passwords do not match');
      return;
    }

    // Attempt password reset
    setIsLoading(true);
    try {
      // Simulate password reset with 1.5 second delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // For now, simulate successful password reset
      // In real app, you would call the reset password API here
      navigation.navigate('auth/login' as never);
    } catch (error) {
      setResetError('Failed to reset password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render Methods
  // Renders the new password input field with show/hide toggle
  const renderNewPasswordField = () => (
    <View className="mb-2">
      <View className="relative">
        <TextInput
          className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${newPasswordError ? 'border-red-500' : ''} pr-12`}
          placeholder="New Password"
          placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
          value={newPassword}
          onChangeText={handleNewPasswordChange}
          secureTextEntry={!showNewPassword}
        />
        <TouchableOpacity
          className="absolute right-3 top-0 bottom-0 justify-center"
          onPress={() => setShowNewPassword(!showNewPassword)}
        >
          <Ionicons 
            name={showNewPassword ? "eye-off" : "eye"} 
            size={20} 
            color={theme.isDark ? '#9CA3AF' : '#6B7280'} 
          />
        </TouchableOpacity>
      </View>
      {newPasswordError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{newPasswordError}</Text>
      ) : null}
    </View>
  );

  // Renders the confirm password input field with show/hide toggle
  const renderConfirmPasswordField = () => (
    <View className="mb-6">
      <View className="relative">
        <TextInput
          className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${confirmPasswordError ? 'border-red-500' : ''} pr-12`}
          placeholder="Confirm New Password"
          placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
          value={confirmPassword}
          onChangeText={handleConfirmPasswordChange}
          secureTextEntry={!showConfirmPassword}
        />
        <TouchableOpacity
          className="absolute right-3 top-0 bottom-0 justify-center"
          onPress={() => setShowConfirmPassword(!showConfirmPassword)}
        >
          <Ionicons 
            name={showConfirmPassword ? "eye-off" : "eye"} 
            size={20} 
            color={theme.isDark ? '#9CA3AF' : '#6B7280'} 
          />
        </TouchableOpacity>
      </View>
      {confirmPasswordError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{confirmPasswordError}</Text>
      ) : null}
    </View>
  );

  // Renders the reset button with loading state
  const renderResetButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleResetPassword}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Resetting...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Reset Password
        </Text>
      )}
    </TouchableOpacity>
  );

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* Background Image Section */}
      <View style={{ height: height * 0.26 }}>
        <ImageBackground
          source={require('../../assets/images/auth/auth_bg.png')}
          style={{ flex: 1 }}
          resizeMode="cover"
        />
      </View>

      {/* Reset Password Form Container */}
      <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
        {/* Welcome Header */}
        <View className="mb-4">
          <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
            Reset Password
          </Text>
          <Text className={`text-start ${theme.colors.textSecondary}`}>
            Set a new password for {email}
          </Text>
        </View>

        {/* Form Fields */}
        {renderNewPasswordField()}
        {renderConfirmPasswordField()}

        {/* Reset Error Display */}
        {resetError ? (
          <View className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <Text className="text-red-600 text-sm text-center">{resetError}</Text>
          </View>
        ) : null}

        {/* Reset Button */}
        {renderResetButton()}

        {/* Back to Login Link */}
        <View className="flex-row justify-center">
          <TouchableOpacity onPress={() => navigation.navigate('auth/login' as never)}>
            <Text className="text-blue-500 font-medium">
              Back to Login
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ResetPasswordScreen; 