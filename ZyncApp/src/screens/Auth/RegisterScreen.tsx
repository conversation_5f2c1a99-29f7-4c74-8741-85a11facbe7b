import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, SafeAreaView, Alert, ImageBackground, Dimensions, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { RootStackParamList } from '../../navigator/StackNavigator';
import { validateEmail, validatePassword, validateName, validateConfirmPassword } from '../../utils/validators';
import authServices from '../../services/authServices';
// ZYNC 

const { width, height } = Dimensions.get('window');

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const RegisterScreen: React.FC = () => {
  // Form data
  const [firstName, setFirstName] = useState('abid');
  const [lastName, setLastName] = useState('mulla');
  const [username, setUsername] = useState('abidmulla');
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('abid@123');
  const [confirmPassword, setConfirmPassword] = useState('abid@123');
  
  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Error states
  const [firstNameError, setFirstNameError] = useState('');
  const [lastNameError, setLastNameError] = useState('');
  const [usernameError, setUsernameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [registerError, setRegisterError] = useState('');
  
  const navigation = useNavigation<NavigationProp>();
  const { register } = useAuth();
  const { theme } = useTheme();

  // Event Handlers
  // Handles first name input changes and clears errors
  const handleFirstNameChange = (text: string) => {
    setFirstName(text);
    if (firstNameError) {
      setFirstNameError('');
    }
  };

  // Handles last name input changes and clears errors
  const handleLastNameChange = (text: string) => {
    setLastName(text);
    if (lastNameError) {
      setLastNameError('');
    }
  };

  // Handles username input changes and clears errors
  const handleUsernameChange = (text: string) => {
    setUsername(text);
    if (usernameError) {
      setUsernameError('');
    }
  };

  // Handles email input changes and clears errors
  const handleEmailChange = (text: string) => {
    setEmail(text);
    if (emailError) {
      setEmailError('');
    }
  };

  // Handles password input changes and clears errors
  const handlePasswordChange = (text: string) => {
    setPassword(text);
    if (passwordError) {
      setPasswordError('');
    }
  };

  // Handles confirm password input changes and clears errors
  const handleConfirmPasswordChange = (text: string) => {
    setConfirmPassword(text);
    if (confirmPasswordError) {
      setConfirmPasswordError('');
    }
  };

  // Handles the registration form submission with validation
  const handleRegister = async () => {
    // Clear all previous errors
    setFirstNameError('');
    setLastNameError('');
    setUsernameError('');
    setEmailError('');
    setPasswordError('');
    setConfirmPasswordError('');
    setRegisterError('');

    // Validate first name
    let hasFirstNameError = false;
    if (!firstName) {
      setFirstNameError('First name is required');
      hasFirstNameError = true;
    } else if (!validateName(firstName)) {
      setFirstNameError('Please enter a valid first name');
      hasFirstNameError = true;
    }

    // Validate last name
    let hasLastNameError = false;
    if (!lastName) {
      setLastNameError('Last name is required');
      hasLastNameError = true;
    } else if (!validateName(lastName)) {
      setLastNameError('Please enter a valid last name');
      hasLastNameError = true;
    }

    // Validate username
    let hasUsernameError = false;
    if (!username) {
      setUsernameError('Username is required');
      hasUsernameError = true;
    } else if (username.length < 3) {
      setUsernameError('Username must be at least 3 characters long');
      hasUsernameError = true;
    }

    // Validate email
    let hasEmailError = false;
    if (!email) {
      setEmailError('Email is required');
      hasEmailError = true;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      hasEmailError = true;
    }

    // Validate password
    let hasPasswordError = false;
    if (!password) {
      setPasswordError('Password is required');
      hasPasswordError = true;
    } else if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters long');
      hasPasswordError = true;
    }

    // Validate confirm password
    let hasConfirmPasswordError = false;
    if (!confirmPassword) {
      setConfirmPasswordError('Please confirm your password');
      hasConfirmPasswordError = true;
    } else if (!validateConfirmPassword(password, confirmPassword)) {
      setConfirmPasswordError('Passwords do not match');
      hasConfirmPasswordError = true;
    }

    // Stop if validation errors exist
    if (hasFirstNameError || hasLastNameError || hasUsernameError || hasEmailError || hasPasswordError || hasConfirmPasswordError) {
      return;
    }

    // Attempt registration
    setIsLoading(true);
    try {
      // Add 1.5 second delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      const fullName = `${firstName} ${lastName}`;
      const response = await authServices.register({
        name: fullName,
        username,
        email,
        password,
        authProvider: 'manual'
      });

      if (response.success && response.data) {
                 // Registration successful, navigate to OTP verification
         navigation.navigate('auth/otp-verification' as any, {
           email,
           fromScreen: 'register',
           message: response.data.message || 'Registration successful. Please verify your email with OTP.'
         });
      } else {
        // Handle specific error messages from backend
        setRegisterError(response.error || 'Registration failed. Please try again.');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      setRegisterError('Network error. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render Methods
  // Renders the first name input field with validation error display
  const renderFirstNameField = () => (
    <View className="flex-1 mr-1">
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${firstNameError ? 'border-red-500' : ''}`}
        placeholder="First Name"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={firstName}
        onChangeText={handleFirstNameChange}
        autoCapitalize="words"
      />
      {firstNameError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{firstNameError}</Text>
      ) : null}
    </View>
  );

  // Renders the last name input field with validation error display
  const renderLastNameField = () => (
    <View className="flex-1 ml-1">
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${lastNameError ? 'border-red-500' : ''}`}
        placeholder="Last Name"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={lastName}
        onChangeText={handleLastNameChange}
        autoCapitalize="words"
      />
      {lastNameError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{lastNameError}</Text>
      ) : null}
    </View>
  );

  // Renders the username input field with validation error display
  const renderUsernameField = () => (
    <View className="mb-2">
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${usernameError ? 'border-red-500' : ''}`}
        placeholder="Username"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={username}
        onChangeText={handleUsernameChange}
        autoCapitalize="none"
        autoCorrect={false}
      />
      {usernameError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{usernameError}</Text>
      ) : null}
    </View>
  );

  // Renders the email input field with validation error display
  const renderEmailField = () => (
    <View className="mb-2">
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${emailError ? 'border-red-500' : ''}`}
        placeholder="Email"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={email}
        onChangeText={handleEmailChange}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
      />
      {emailError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{emailError}</Text>
      ) : null}
    </View>
  );

  // Renders the password input field with show/hide toggle
  const renderPasswordField = () => (
    <View className="mb-2">
      <View className="relative">
        <TextInput
          className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${passwordError ? 'border-red-500' : ''} pr-12`}
          placeholder="Password"
          placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
          value={password}
          onChangeText={handlePasswordChange}
          secureTextEntry={!showPassword}
        />
        <TouchableOpacity
          className="absolute right-3 top-0 bottom-0 justify-center"
          onPress={() => setShowPassword(!showPassword)}
        >
          <Ionicons 
            name={showPassword ? "eye-off" : "eye"} 
            size={20} 
            color={theme.isDark ? '#9CA3AF' : '#6B7280'} 
          />
        </TouchableOpacity>
      </View>
      {passwordError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{passwordError}</Text>
      ) : null}
    </View>
  );

  // Renders the confirm password input field with show/hide toggle
  const renderConfirmPasswordField = () => (
    <View className="mb-6">
      <View className="relative">
        <TextInput
          className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${confirmPasswordError ? 'border-red-500' : ''} pr-12`}
          placeholder="Confirm Password"
          placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
          value={confirmPassword}
          onChangeText={handleConfirmPasswordChange}
          secureTextEntry={!showConfirmPassword}
        />
        <TouchableOpacity
          className="absolute right-3 top-0 bottom-0 justify-center"
          onPress={() => setShowConfirmPassword(!showConfirmPassword)}
        >
          <Ionicons 
            name={showConfirmPassword ? "eye-off" : "eye"} 
            size={20} 
            color={theme.isDark ? '#9CA3AF' : '#6B7280'} 
          />
        </TouchableOpacity>
      </View>
      {confirmPasswordError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{confirmPasswordError}</Text>
      ) : null}
    </View>
  );

  // Renders the register button with loading state
  const renderRegisterButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleRegister}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Creating Account...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Create Account
        </Text>
      )}
    </TouchableOpacity>
  );

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* Top Image Section */}
      <View style={{ height: height * 0.26 }}>
        <ImageBackground
          source={require('../../assets/images/auth/auth_bg.png')}
          style={{ flex: 1 }}
          resizeMode="cover"
        />
      </View>

      {/* Register Form Section */}
      <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
        {/* Welcome Title */}
        <View className="mb-4">
          <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
            Create Account
          </Text>
          <Text className={`text-start ${theme.colors.textSecondary}`}>
            Sign up to get started
          </Text>
        </View>

        {/* Name Fields */}
        <View className="flex-row mb-2">
          {renderFirstNameField()}
          {renderLastNameField()}
        </View>

        {/* Username Field */}
        {renderUsernameField()}

        {/* Form Fields */}
        {renderEmailField()}
        {renderPasswordField()}
        {renderConfirmPasswordField()}

        {/* Register Error Display */}
        {registerError ? (
          <View className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <Text className="text-red-600 text-sm text-center">{registerError}</Text>
          </View>
        ) : null}

        {/* Register Button */}
        {renderRegisterButton()}

        {/* Sign In Link */}
        <View className="flex-row justify-center">
          <Text className={theme.colors.textSecondary}>
            Already have an account?{' '}
          </Text>
          <TouchableOpacity onPress={() => navigation.navigate('auth/login')}>
            <Text className="text-blue-500 font-medium">
              Sign In
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default RegisterScreen;
