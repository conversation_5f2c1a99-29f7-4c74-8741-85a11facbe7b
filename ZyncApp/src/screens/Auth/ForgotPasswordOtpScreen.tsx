import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, SafeAreaView, ImageBackground, Dimensions, ActivityIndicator } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { validateOtp, validatePassword, validateConfirmPassword } from '../../utils/validators';
import authServices from '../../services/authServices';
import Ionicons from 'react-native-vector-icons/Ionicons';
// ZYNC 

// Constants
const { height } = Dimensions.get('window');

// Main Forgot Password OTP Component
const ForgotPasswordOtpScreen: React.FC = () => {
  // Form data
  const [otp, setOtp] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Error states
  const [otpError, setOtpError] = useState('');
  const [newPasswordError, setNewPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [verificationError, setVerificationError] = useState('');
  
  // Navigation and context
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();

  // Get params from route (passed from ForgotPasswordScreen)
  const email = (route.params as any)?.email || 'your email';
  const message = (route.params as any)?.message || 'Please enter the OTP sent to your email.';

  // Event Handlers
  // Handles OTP input changes and clears errors
  const handleOtpChange = (text: string) => {
    setOtp(text);
    if (otpError) {
      setOtpError('');
    }
  };

  // Handles new password input changes and clears errors
  const handleNewPasswordChange = (text: string) => {
    setNewPassword(text);
    if (newPasswordError) {
      setNewPasswordError('');
    }
  };

  // Handles confirm password input changes and clears errors
  const handleConfirmPasswordChange = (text: string) => {
    setConfirmPassword(text);
    if (confirmPasswordError) {
      setConfirmPasswordError('');
    }
  };

  // Handles the password reset with OTP verification
  const handleResetPassword = async () => {
    // Clear previous errors
    setOtpError('');
    setNewPasswordError('');
    setConfirmPasswordError('');
    setVerificationError('');

    // Validate OTP
    if (!otp) {
      setOtpError('Please enter the OTP code');
      return;
    }

    if (!validateOtp(otp)) {
      setOtpError('Please enter a valid 6-digit OTP code');
      return;
    }

    // Validate new password
    if (!newPassword) {
      setNewPasswordError('Please enter a new password');
      return;
    }

    if (newPassword.length < 6) {
      setNewPasswordError('Password must be at least 6 characters long');
      return;
    }

    // Validate confirm password
    if (!confirmPassword) {
      setConfirmPasswordError('Please confirm your password');
      return;
    }

    if (!validateConfirmPassword(newPassword, confirmPassword)) {
      setConfirmPasswordError('Passwords do not match');
      return;
    }

    // Attempt password reset
    setIsLoading(true);
    try {
      // Add 1.5 second delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      const response = await authServices.verifyForgotPasswordOtp({
        email,
        otp,
        newPassword
      });

      if (response.success && response.data) {
        // Password reset successful, navigate to login
        (navigation as any).navigate('auth/login');
      } else {
        // Handle specific error messages from backend
        setVerificationError(response.error || 'Password reset failed. Please try again.');
      }
    } catch (error: any) {
      console.error('Password reset error:', error);
      setVerificationError('Network error. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handles resending OTP
  const handleResendOtp = async () => {
    setIsResending(true);
    setVerificationError('');
    try {
      // Add 1 second delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000));

      const response = await authServices.forgotPassword({ email });

      if (response.success && response.data) {
        setVerificationError('OTP resent to your email successfully.');
      } else {
        setVerificationError(response.error || 'Failed to resend OTP. Please try again.');
      }
    } catch (error: any) {
      console.error('Resend OTP error:', error);
      setVerificationError('Network error. Please check your connection and try again.');
    } finally {
      setIsResending(false);
    }
  };

  // Render Methods
  // Renders the OTP input field with validation error display
  const renderOtpField = () => (
    <View className="mb-4">
      <Text className={`text-sm font-medium mb-2 ${theme.colors.text}`}>Enter OTP</Text>
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-3 ${theme.colors.text} text-center text-xl ${otpError ? 'border-red-500' : ''}`}
        placeholder="000000"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={otp}
        onChangeText={handleOtpChange}
        keyboardType="numeric"
        maxLength={6}
        style={{ textAlignVertical: 'center' }}
      />
      {otpError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1 text-center">{otpError}</Text>
      ) : null}
    </View>
  );

  // Renders the new password input field with show/hide toggle
  const renderNewPasswordField = () => (
    <View className="mb-4">
      <Text className={`text-sm font-medium mb-2 ${theme.colors.text}`}>New Password</Text>
      <View className="relative">
        <TextInput
          className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${newPasswordError ? 'border-red-500' : ''} pr-12`}
          placeholder="Enter new password"
          placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
          value={newPassword}
          onChangeText={handleNewPasswordChange}
          secureTextEntry={!showNewPassword}
        />
        <TouchableOpacity
          className="absolute right-3 top-0 bottom-0 justify-center"
          onPress={() => setShowNewPassword(!showNewPassword)}
        >
          <Ionicons
            name={showNewPassword ? "eye-off" : "eye"}
            size={20}
            color={theme.isDark ? '#9CA3AF' : '#6B7280'}
          />
        </TouchableOpacity>
      </View>
      {newPasswordError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{newPasswordError}</Text>
      ) : null}
    </View>
  );

  // Renders the confirm password input field with show/hide toggle
  const renderConfirmPasswordField = () => (
    <View className="mb-6">
      <Text className={`text-sm font-medium mb-2 ${theme.colors.text}`}>Confirm Password</Text>
      <View className="relative">
        <TextInput
          className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${confirmPasswordError ? 'border-red-500' : ''} pr-12`}
          placeholder="Confirm new password"
          placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
          value={confirmPassword}
          onChangeText={handleConfirmPasswordChange}
          secureTextEntry={!showConfirmPassword}
        />
        <TouchableOpacity
          className="absolute right-3 top-0 bottom-0 justify-center"
          onPress={() => setShowConfirmPassword(!showConfirmPassword)}
        >
          <Ionicons
            name={showConfirmPassword ? "eye-off" : "eye"}
            size={20}
            color={theme.isDark ? '#9CA3AF' : '#6B7280'}
          />
        </TouchableOpacity>
      </View>
      {confirmPasswordError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{confirmPasswordError}</Text>
      ) : null}
    </View>
  );

  // Renders the reset password button with loading state
  const renderResetButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleResetPassword}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Resetting Password...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Reset Password
        </Text>
      )}
    </TouchableOpacity>
  );

  // Renders the resend OTP section
  const renderResendSection = () => (
    <View className="flex-row justify-center mb-4">
      <Text className={`${theme.colors.textSecondary} mr-2`}>
        Didn't receive the code?
      </Text>
      <TouchableOpacity onPress={handleResendOtp} disabled={isResending}>
        <Text className={`font-medium ${isResending ? 'text-gray-400' : 'text-blue-500'}`}>
          {isResending ? 'Sending...' : 'Resend'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* Background Image Section */}
      <View style={{ height: height * 0.26 }}>
        <ImageBackground
          source={require('../../assets/images/auth/auth_bg.png')}
          style={{ flex: 1 }}
          resizeMode="cover"
        />
      </View>

      {/* OTP Verification Form Container */}
      <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
        {/* Welcome Header */}
        <View className="mb-4">
          <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
            Reset Password
          </Text>
          <Text className={`text-start ${theme.colors.textSecondary}`}>
            Enter the OTP sent to {email} and your new password
          </Text>
        </View>

        {/* Form Fields */}
        {renderOtpField()}
        {renderNewPasswordField()}
        {renderConfirmPasswordField()}

        {/* Verification Error Display */}
        {verificationError ? (
          <View className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <Text className="text-red-600 text-sm text-center">{verificationError}</Text>
          </View>
        ) : null}

        {/* Reset Password Button */}
        {renderResetButton()}

        {/* Resend OTP Section */}
        {renderResendSection()}

        {/* Back to Forgot Password Link */}
        <View className="flex-row justify-center">
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text className="text-blue-500 font-medium">
              Back to Forgot Password
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ForgotPasswordOtpScreen;
