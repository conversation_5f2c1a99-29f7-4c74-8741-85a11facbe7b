# API Integration Guide

## Overview
This guide explains how the frontend authentication screens are integrated with the backend APIs, including error handling and loading states.

## Backend API Endpoints

### Authentication Routes
- `POST /auth/register` - Register a new user
- `POST /auth/verify-register-otp` - Verify registration OTP
- `POST /auth/regenerate-otp` - Regenerate registration OTP
- `POST /auth/login` - User login
- `POST /auth/forgot-password` - Request password reset OTP
- `POST /auth/verify-forgot-password-otp` - Verify OTP and reset password
- `POST /auth/logout` - User logout

## Frontend Integration

### Services Structure
- `authServices.ts` - Handles all authentication API calls
- `apiController.ts` - Generic HTTP client with error handling

### Error Handling
All API calls include comprehensive error handling:
- Network errors
- Server errors (4xx, 5xx)
- Validation errors
- Backend-specific error messages

### Loading States
Each authentication action includes:
- 1.5-second minimum loading time for better UX
- Loading indicators on buttons
- Disabled state during API calls

### Error Display
Errors are displayed above buttons with:
- Red background for visibility
- Specific error messages from backend
- Fallback messages for network issues

## Screen-Specific Integration

### LoginScreen
- Validates email and password
- Handles multiple error scenarios:
  - Invalid credentials
  - Account not verified
  - Account deactivated
  - Wrong auth provider
- Navigates to OTP verification if needed

### RegisterScreen
- Collects: name, username, email, password
- Validates all fields before submission
- Navigates to OTP verification on success

### OtpVerificationScreen
- Handles registration OTP verification
- Supports resending OTP
- Navigates to main app on success

### ForgotPasswordScreen
- Sends password reset OTP
- Navigates to password reset form

### ForgotPasswordOtpScreen
- Combined OTP verification and password reset
- Validates OTP and new password
- Resets password in single API call

## Configuration

### API Base URL
Update the base URL in `apiController.ts`:
```typescript
this.baseURL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000';
```

### Environment Variables
Set in your environment:
- `REACT_APP_API_BASE_URL` - Backend API URL

## Error Messages
The app displays specific error messages from the backend:
- "Email already registered"
- "Username already taken"
- "Invalid password"
- "Account is blocked. Please contact support."
- "Account is deactivated. Please contact support."
- "OTP expired. Please request a new one."
- And more...

## Testing
To test the integration:
1. Ensure backend is running
2. Update API base URL
3. Test each authentication flow
4. Verify error handling with invalid data
5. Test network error scenarios
