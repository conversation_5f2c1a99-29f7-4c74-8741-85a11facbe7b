const express = require('express');
const router = express.Router();
const authController = require('../controller/auth_controller/auth_controller');

// ========================================
// @zync/ AUTHENTICATION API ROUTES
// ========================================

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: User ID
 *         name:
 *           type: string
 *           description: User's full name
 *         username:
 *           type: string
 *           description: Username
 *         email:
 *           type: string
 *           description: User's email address
 *         is_email_verified:
 *           type: boolean
 *           description: Email verification status
 *         isActive:
 *           type: boolean
 *           description: Account active status
 *         role_id:
 *           type: integer
 *           description: User's role ID
 *     RegisterRequest:
 *       type: object
 *       required:
 *         - name
 *         - username
 *         - email
 *         - password
 *       properties:
 *         name:
 *           type: string
 *           description: User's full name
 *         username:
 *           type: string
 *           description: Username (must be unique)
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         password:
 *           type: string
 *           minLength: 6
 *           description: Password (minimum 6 characters)
 *         authProvider:
 *           type: string
 *           description: Authentication provider (optional, defaults to 'manual')
 *           example: "manual"
 *     VerifyOtpRequest:
 *       type: object
 *       required:
 *         - email
 *         - otp
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         otp:
 *           type: string
 *           description: 6-digit OTP code
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         password:
 *           type: string
 *           description: User's password
 *     ForgotPasswordRequest:
 *       type: object
 *       required:
 *         - email
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *     ResetPasswordRequest:
 *       type: object
 *       required:
 *         - email
 *         - otp
 *         - newPassword
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         otp:
 *           type: string
 *           description: 6-digit OTP code
 *         newPassword:
 *           type: string
 *           minLength: 6
 *           description: New password (minimum 6 characters)
 *     LogoutRequest:
 *       type: object
 *       required:
 *         - email
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *     JWTTokenPayload:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: User ID
 *         email:
 *           type: string
 *           description: User's email address
 *         username:
 *           type: string
 *           description: Username
 *         auth_provider:
 *           type: string
 *           description: Authentication provider (manual, google, apple, etc.)
 *         role_id:
 *           type: integer
 *           description: User's role ID
 *         iat:
 *           type: integer
 *           description: Token issued at timestamp
 *         exp:
 *           type: integer
 *           description: Token expiration timestamp
 */

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: Register a new user
 *     description: Register a new user account with email verification via OTP
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterRequest'
 *     responses:
 *       201:
 *         description: Registration successful, OTP sent to email
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Registration successful. OTP sent to email for verification."
 *                 second_message:
 *                   type: string
 *                   example: "OTP valid for 10 minutes"
 *                 token:
 *                   type: string
 *                   description: JWT authentication token
 *                   example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                 authExpireTime:
 *                   type: string
 *                   description: JWT token expiration time
 *                   example: "24h"
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 otp:
 *                   type: string
 *                   description: OTP code (remove in production)
 *                 otpExpireAt:
 *                   type: string
 *                   format: date-time
 *                 otpExpireTime:
 *                   type: string
 *                   example: "10 minutes"
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 */
router.post('/register', authController.register);

/**
 * @swagger
 * /auth/verify-register-otp:
 *   post:
 *     summary: Verify registration OTP
 *     description: Verify the OTP sent during registration to activate the account
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/VerifyOtpRequest'
 *     responses:
 *       200:
 *         description: Email verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Email verified successfully. Registration completed."
 *                 token:
 *                   type: string
 *                   description: JWT authentication token
 *                   example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                 authExpireTime:
 *                   type: string
 *                   description: JWT token expiration time
 *                   example: "24h"
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         description: Invalid OTP or email
 *       500:
 *         description: Internal server error
 */
router.post('/verify-register-otp', authController.verifyRegisterOtp);

/**
 * @swagger
 * /auth/regenerate-otp:
 *   post:
 *     summary: Regenerate registration OTP
 *     description: Resend a new OTP for email verification if the account is not yet verified
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *                 example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: OTP regenerated and sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "OTP regenerated and sent to your email."
 *                 second_message:
 *                   type: string
 *                   example: "OTP valid for 10 minutes"
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 otp:
 *                   type: string
 *                   description: OTP code (remove in production)
 *                   example: "123456"
 *                 otpExpireAt:
 *                   type: string
 *                   format: date-time
 *                   description: OTP expiration timestamp
 *                   example: "2025-01-12T18:30:00.000Z"
 *                 otpExpireTime:
 *                   type: string
 *                   description: OTP expiration time in human-readable format
 *                   example: "10 minutes"
 *                 email_sent:
 *                   type: boolean
 *                   description: Whether the email was sent successfully
 *                   example: true
 *       400:
 *         description: Validation error or email already verified
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Email already verified"
 *       403:
 *         description: Account blocked
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Account is blocked. Please contact support."
 *       404:
 *         description: Email not registered
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "This email is not registered"
 *       500:
 *         description: Internal server error
 */
router.post('/regenerate-otp', authController.regenerateOTP);

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: User login
 *     description: Authenticate user and return account status
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Login successful"
 *                 token:
 *                   type: string
 *                   description: JWT authentication token
 *                   example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                 authExpireTime:
 *                   type: string
 *                   description: JWT token expiration time
 *                   example: "24h"
 *                 is_active:
 *                   type: integer
 *                   enum: [1]
 *                   example: 1
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: Invalid credentials
 *       403:
 *         description: Account not verified, blocked, or deactivated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Account is deactivated. Please contact support."
 *                 auth_provider:
 *                   type: string
 *                   description: Authentication provider used during registration
 *                   example: "google"
 *                 is_active:
 *                   type: integer
 *                   enum: [0]
 *                   example: 0
 *                 email:
 *                   type: string
 *                   format: email
 *                   description: User's email address
 *                   example: "<EMAIL>"
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       500:
 *         description: Internal server error
 */
router.post('/login', authController.login);

/**
 * @swagger
 * /auth/forgot-password:
 *   post:
 *     summary: Request password reset
 *     description: Send OTP to user's email for password reset
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ForgotPasswordRequest'
 *     responses:
 *       200:
 *         description: Password reset OTP sent
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Password reset OTP sent to your email."
 *                 second_message:
 *                   type: string
 *                   example: "OTP valid for 10 minutes"
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 otp:
 *                   type: string
 *                   description: OTP code (remove in production)
 *                 otpExpireAt:
 *                   type: string
 *                   format: date-time
 *                 otpExpireTime:
 *                   type: string
 *                   example: "10 minutes"
 *       404:
 *         description: Email not found
 *       403:
 *         description: Account blocked or email not verified
 *       500:
 *         description: Internal server error
 */
router.post('/forgot-password', authController.forgotPassword);

/**
 * @swagger
 * /auth/verify-forgot-password-otp:
 *   post:
 *     summary: Reset password with OTP
 *     description: Verify OTP and update user's password
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ResetPasswordRequest'
 *     responses:
 *       200:
 *         description: Password reset successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Password reset successful. You can now login with your new password."
 *                 token:
 *                   type: string
 *                   description: JWT authentication token
 *                   example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                 authExpireTime:
 *                   type: string
 *                   description: JWT token expiration time
 *                   example: "24h"
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         description: Invalid OTP, expired OTP, or validation error
 *       403:
 *         description: Account blocked or email not verified
 *       500:
 *         description: Internal server error
 */
router.post('/verify-forgot-password-otp', authController.verifyForgotPasswordOtp);

/**
 * @swagger
 * /auth/logout:
 *   post:
 *     summary: User logout
 *     description: Logout user by email
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LogoutRequest'
 *     responses:
 *       200:
 *         description: Logout successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Logout successful"
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Email is required"
 *       403:
 *         description: Account not active
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Account is not active"
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "User not found"
 *       500:
 *         description: Internal server error
 */
router.post('/logout', authController.logout);

module.exports = router;
