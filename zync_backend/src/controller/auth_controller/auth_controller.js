
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const mysqlConnection = require('../../config/connection');
const { sendRegisterOtpEmail, sendForgotPasswordOtpEmail, sendAccountDeactivatedEmail } = require('../../helpers/email-template/auth/auth-email-template');

console.log('🔐 [auth_controller] Initializing authentication controller...');

// JWT Secret (in production, use environment variable)
const JWT_SECRET = process.env.JWT_SECRET;
const JWT_EXPIRE_TIME = process.env.JWT_EXPIRE_TIME;
const OTP_EXPIRE_TIME = process.env.OTP_EXPIRE_TIME;

console.log('📋 [auth_controller] Environment variables loaded:');
console.log(`   JWT_SECRET: ${JWT_SECRET ? '✅ Set' : '❌ Missing'}`);
console.log(`   JWT_EXPIRE_TIME: ${JWT_EXPIRE_TIME || '❌ Missing'}`);
console.log(`   OTP_EXPIRE_TIME: ${OTP_EXPIRE_TIME || '❌ Missing'}`);

// Generate JWT Token
const generateToken = async (user) => {
    console.log('🎫 [auth_controller] Generating JWT token for user:', user.id);
    
    const payload = {
        id: user.id,
        email: user.email,
        username: user.username,
        auth_provider: user.auth_provider,
        role_id: user.role_id
    };

    console.log('📋 [auth_controller] JWT payload:', { ...payload, email: '***' });
    
    const token = await jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRE_TIME });
    console.log('✅ [auth_controller] JWT token generated successfully');
    
    return token;
};

// Generate a unique 6-digit OTP and ensure it's not already in use
const generateUniqueOtp = async () => {
    console.log('🔢 [auth_controller] Generating unique OTP...');
    
    let otpCode;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 10; // Prevent infinite loop

    while (!isUnique && attempts < maxAttempts) {
        // Generate a 6-digit OTP
        otpCode = Math.floor(100000 + Math.random() * 900000).toString();
        console.log(`🔢 [auth_controller] Generated OTP attempt ${attempts + 1}: ${otpCode}`);

        // Check if OTP already exists and is not expired
        const [existingOtp] = await mysqlConnection.execute(
            'SELECT id FROM users WHERE otp_code = ? AND otp_expire_at > NOW()',
            [otpCode]
        );

        if (existingOtp.length === 0) {
            isUnique = true;
            console.log('✅ [auth_controller] Unique OTP generated successfully');
        } else {
            attempts++;
            console.log(`⚠️ [auth_controller] OTP already exists, trying again (attempt ${attempts})`);
        }
    }

    if (!isUnique) {
        console.error('❌ [auth_controller] Failed to generate unique OTP after maximum attempts');
        throw new Error('Unable to generate unique OTP after maximum attempts');
    }

    console.log(`🎯 [auth_controller] Final unique OTP: ${otpCode}`);
    return otpCode;
};

const register = async (req, res) => {
    console.log('📝 [auth_controller] Registration request received');
    console.log('📋 [auth_controller] Request body:', { 
        name: req.body.name ? '***' : 'missing',
        username: req.body.username ? '***' : 'missing',
        email: req.body.email ? '***' : 'missing',
        password: req.body.password ? '***' : 'missing',
        authProvider: req.body.authProvider || 'not provided'
    });
    
    try {
        const { name, username, email, password, authProvider } = req.body;

        // Validation
        if (!name || !username || !email || !password) {
            console.log('❌ [auth_controller] Registration validation failed - missing required fields');
            return res.status(400).json({
                message: 'All fields are required: name, username, email, password' 
            });
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            console.log('❌ [auth_controller] Registration validation failed - invalid email format');
            return res.status(400).json({ 
                message: 'Invalid email format' 
            });
        }
        console.log('✅ [auth_controller] Email format validation passed');

        // Password strength validation (minimum 6 characters)
        if (password.length < 6) {
            console.log('❌ [auth_controller] Registration validation failed - password too short');
            return res.status(400).json({ 
                message: 'Password must be at least 8 characters long'
            });
        }
        console.log('✅ [auth_controller] Password strength validation passed');

        // Set default auth provider if not provided
        const authProviderValue = authProvider || 'manual';
        console.log(`🔐 [auth_controller] Auth provider set to: ${authProviderValue}`);

        // Check if email already exists
        console.log('🔍 [auth_controller] Checking if email already exists...');
        const [existingEmail] = await mysqlConnection.execute(
            'SELECT id, is_email_verified FROM users WHERE email = ?',
            [email]
        );

        if (existingEmail.length > 0) {
            console.log('❌ [auth_controller] Registration failed - email already registered');
            return res.status(400).json({
                message: 'Email already registered' 
            });
        }
        console.log('✅ [auth_controller] Email availability check passed');

        // Check if username already exists
        console.log('🔍 [auth_controller] Checking if username already exists...');
        const [existingUsername] = await mysqlConnection.execute(
            'SELECT id FROM users WHERE username = ?',
            [username]
        );

        if (existingUsername.length > 0) {
            console.log('❌ [auth_controller] Registration failed - username already taken');
            return res.status(400).json({
                message: 'Username already taken' 
            });
        }
        console.log('✅ [auth_controller] Username availability check passed');

        // Hash password
        console.log('🔒 [auth_controller] Hashing password...');
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(password, saltRounds);
        console.log('✅ [auth_controller] Password hashed successfully');

        // Generate unique OTP
        console.log('🔢 [auth_controller] Generating OTP for email verification...');
        const otpCode = await generateUniqueOtp();
        const otpExpireAt = new Date(Date.now() + (parseInt(OTP_EXPIRE_TIME) * 60 * 1000)); // OTP_EXPIRE_TIME minutes from now
        console.log(`⏰ [auth_controller] OTP expires at: ${otpExpireAt}`);

        // Insert user with OTP
        console.log('💾 [auth_controller] Inserting new user into database...');
        const [result] = await mysqlConnection.execute(`
            INSERT INTO users (
                name, user_name, username, email, password, 
                auth_provider, is_email_verified, is_mobile_verified,
                otp_code, otp_expire_at, is_blocked, is_deleted, isActive,
                meta, secret
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            name,
            username, // user_name same as username
            username,
            email,
            hashedPassword,
            authProviderValue, // use the auth provider from request or default to 'manual'
            0, // email not verified yet
            0, // mobile not verified (will be used later)
            otpCode,
            otpExpireAt,
            0, // not blocked
            0, // not deleted
            0, // not active until email verified
            JSON.stringify({ TFARequire: false }),
            JSON.stringify({})
        ]);

        const userId = result.insertId;
        console.log(`✅ [auth_controller] User created successfully with ID: ${userId}`);

        // Get default user role
        console.log('👥 [auth_controller] Assigning default user role...');
        const [userRole] = await mysqlConnection.execute(
            'SELECT id FROM roles WHERE name = ?',
            ['user']
        );

        let roleId = null;
        if (userRole.length > 0) {
            roleId = userRole[0].id;
            console.log(`👥 [auth_controller] Found user role with ID: ${roleId}`);
            // Assign default user role
            await mysqlConnection.execute(
                'INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)',
                [userId, roleId]
            );
            console.log(`✅ [auth_controller] User role assigned successfully`);
        } else {
            console.log('⚠️ [auth_controller] No default user role found');
        }

        // Generate JWT token for auto-login after registration
        console.log('🎫 [auth_controller] Generating JWT token for new user...');
        const token = await generateToken({
            id: userId,
            name,
            username,
            email,
            auth_provider: authProviderValue,
            role_id: roleId
        });

        // Send OTP email
        console.log('📧 [auth_controller] Sending registration OTP email...');
        const emailResult = await sendRegisterOtpEmail(email, otpCode, name);
        console.log(`📧 [auth_controller] Email send result: ${emailResult.success ? 'Success' : 'Failed'}`);
        
        // Return success response
        console.log('🎉 [auth_controller] Registration completed successfully');
        res.status(201).json({
            message: 'Registration successful. OTP sent to email for verification.',
            second_message: `OTP valid for ${OTP_EXPIRE_TIME} minutes`,
            token: token,
            authExpireTime: JWT_EXPIRE_TIME,
            user: {
                id: userId,
                name,
                username,
                email,
                is_email_verified: false,
                role_id: roleId
            },
            otp: otpCode, // Remove this in production
            otpExpireAt,
            otpExpireTime: `${OTP_EXPIRE_TIME} minutes`,
            email_sent: emailResult.success
        });

    } catch (error) {
        console.error('❌ [auth_controller] Registration error:', error);
        console.error('📚 [auth_controller] Error stack:', error.stack);
        res.status(500).json({ message: 'Internal server error' });
    }
};

const verifyRegisterOtp = async (req, res) => {
    console.log('🔍 [auth_controller] OTP verification request received');
    console.log('📋 [auth_controller] Request bodySs:', req.body.email 
    );
    console.log('📋 [auth_controller] Request body:', req.body.otp 
    );
    
    try {
        const { email, otp } = req.body;

        // Clean and validate input
        const cleanEmail = email ? email.trim().toLowerCase() : '';
        const cleanOtp = otp ? otp.toString().trim() : '';

        // Validation
        if (!cleanEmail || !cleanOtp) {
            console.log('❌ [auth_controller] OTP verification failed - missing required fields');
            return res.status(400).json({
                message: 'Email and OTP are required'
            });
        }

        // Find user with matching email and OTP
        console.log('🔍 [auth_controller] Searching for user with matching email and OTP...');
        console.log(`🔍 [auth_controller] Looking for email: "${cleanEmail}" and OTP: "${cleanOtp}"`);

        // First, let's check if user exists with this email
        const [emailCheck] = await mysqlConnection.execute(`
            SELECT u.id, u.name, u.username, u.email, u.otp_code, u.otp_expire_at, u.is_email_verified, ur.role_id
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            WHERE u.email = ? AND u.is_deleted = 0
        `, [cleanEmail]);

        if (emailCheck.length === 0) {
            console.log('❌ [auth_controller] No user found with this email');
            return res.status(400).json({
                message: 'Invalid email'
            });
        }

        console.log(`📋 [auth_controller] User found with email. OTP in DB: "${emailCheck[0].otp_code}", OTP provided: "${cleanOtp}"`);
        console.log(`📋 [auth_controller] OTP expiry: ${emailCheck[0].otp_expire_at}`);

        const [users] = await mysqlConnection.execute(`
            SELECT u.id, u.name, u.username, u.email, u.otp_code, u.otp_expire_at, u.is_email_verified, ur.role_id
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            WHERE u.email = ? AND u.otp_code = ? AND u.is_deleted = 0
        `, [cleanEmail, cleanOtp]);

        if (users.length === 0) {
            console.log('❌ [auth_controller] OTP verification failed - OTP mismatch');
            console.log(`❌ [auth_controller] Expected OTP: "${emailCheck[0].otp_code}", Received OTP: "${cleanOtp}"`);
            return res.status(400).json({
                message: 'Invalid OTP. Please check the code and try again.'
            });
        }
        console.log(`✅ [auth_controller] User found with ID: ${users[0].id}`);

        const user = users[0];

        // Check if user is already verified
        if (user.is_email_verified) {
            console.log('❌ [auth_controller] OTP verification failed - email already verified');
            return res.status(400).json({
                message: 'Email already verified' 
            });
        }
        console.log('✅ [auth_controller] Email verification status check passed');

        // Check if OTP is expired
        const now = new Date();
        const otpExpireAt = new Date(user.otp_expire_at);
        console.log(`⏰ [auth_controller] OTP expiry check - Current: ${now}, Expires: ${otpExpireAt}`);
        
        if (now > otpExpireAt) {
            console.log('❌ [auth_controller] OTP verification failed - OTP expired');
            return res.status(400).json({
                message: 'OTP expired. Please request a new one.' 
            });
        }
        console.log('✅ [auth_controller] OTP expiry check passed');

        // Verify email, activate account, and clear OTP
        console.log('✅ [auth_controller] Updating user verification status...');
        await mysqlConnection.execute(`
            UPDATE users 
            SET is_email_verified = 1, isActive = 1, otp_code = NULL, otp_expire_at = NULL 
            WHERE id = ?
        `, [user.id]);
        console.log('✅ [auth_controller] User email verified and account activated');

        // Generate JWT token for auto-login after verification
        console.log('🎫 [auth_controller] Generating JWT token for verified user...');
        const token = await generateToken(user);

        console.log('🎉 [auth_controller] OTP verification completed successfully');
        res.status(200).json({
            message: 'Email verified successfully. Registration completed.',
            token: token,
            authExpireTime: JWT_EXPIRE_TIME,
            user: {
                id: user.id,
                name: user.name,
                username: user.username,
                email: user.email,
                is_email_verified: true,
                isActive: true,
                role_id: user.role_id
            }
        });

    } catch (error) {
        console.error('❌ [auth_controller] OTP verification error:', error);
        console.error('📚 [auth_controller] Error stack:', error.stack);
        res.status(500).json({ message: 'Internal server error' });
    }
};

const login = async (req, res) => {
    console.log('🔐 [auth_controller] Login request received');
    console.log('📋 [auth_controller] Request body:', { 
        email: req.body.email ? '***' : 'missing',
        password: req.body.password ? '***' : 'missing'
    });
    
    try {
        const { email, password } = req.body;

        // Validation
        if (!email || !password) {
            console.log('❌ [auth_controller] Login failed - missing required fields');
            return res.status(400).json({
                message: 'Email and password are required'
            });
        }

        // Find user by email
        console.log('🔍 [auth_controller] Searching for user by email...');
        const [users] = await mysqlConnection.execute(`
            SELECT u.id, u.name, u.username, u.email, u.password, u.auth_provider, u.is_email_verified, u.isActive, u.is_blocked, u.is_deleted, ur.role_id
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            WHERE u.email = ? AND u.is_deleted = 0
        `, [email]);

        if (users.length === 0) {
            console.log('❌ [auth_controller] Login failed - email not registered');
            return res.status(401).json({
                message: 'This email is not registered'
            });
        }
        console.log(`✅ [auth_controller] User found with ID: ${users[0].id}`);

        const user = users[0];

        // Check if user is blocked
        if (user.is_blocked) {
            console.log('❌ [auth_controller] Login failed - account blocked');
            return res.status(403).json({
                message: 'Account is blocked. Please contact support.'
            });
        }
        console.log('✅ [auth_controller] Account block status check passed');

        // Check if user is deleted
        if (user.is_deleted) {
            console.log('❌ [auth_controller] Login failed - account deleted');
            return res.status(401).json({
                message: 'This email is deleted contact support'
            });
        }
        console.log('✅ [auth_controller] Account deletion status check passed');

        // Check if email is verified
        if (!user.is_email_verified) {
            console.log('❌ [auth_controller] Login failed - email not verified');
            return res.status(403).json({
                message: 'Account is registered but not activated. Please verify your email with OTP.',
                is_active: 0,
                user: {
                    id: user.id,
                    name: user.name,
                    username: user.username,
                    email: user.email,
                    is_email_verified: false,
                    role_id: user.role_id
                }
            });
        }
        console.log('✅ [auth_controller] Email verification status check passed');

        // Check auth provider for login method
        console.log(`🔐 [auth_controller] Auth provider check - User provider: ${user.auth_provider}`);
        if (user.auth_provider !== 'manual') {
            console.log('❌ [auth_controller] Login failed - wrong auth provider');
            return res.status(403).json({
                message: `You registered using ${user.auth_provider} authentication. Please use the same provider to log in.`,
                auth_provider: user.auth_provider,
                is_active: 0
            });
        }
        console.log('✅ [auth_controller] Auth provider check passed');

        // Verify password (only for local/manual auth providers)
        console.log('🔒 [auth_controller] Verifying password...');
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            console.log('❌ [auth_controller] Login failed - invalid password');
            return res.status(401).json({
                message: 'Invalid password'
            });
        }
        console.log('✅ [auth_controller] Password verification passed');

        // Check if account is active
        if (!user.isActive) {
            console.log('❌ [auth_controller] Login failed - account deactivated');
            // Send account deactivated email notification
            await sendAccountDeactivatedEmail(user.email, user.name);
            console.log('📧 [auth_controller] Account deactivation email sent');
            
            return res.status(403).json({
                message: 'Account is deactivated. Please contact support.',
                is_active: 0,
                email: user.email
            });
        }
        console.log('✅ [auth_controller] Account active status check passed');

        // Generate JWT token
        console.log('🎫 [auth_controller] Generating JWT token for login...');
        const token = await generateToken(user);

        // Login successful
        console.log('🎉 [auth_controller] Login completed successfully');
        res.status(200).json({
            message: 'Login successful',
            token: token,
            authExpireTime: JWT_EXPIRE_TIME,
            is_active: 1,
            user: {
                id: user.id,
                name: user.name,
                username: user.username,
                email: user.email,
                is_email_verified: true,
                role_id: user.role_id
            }
        });

    } catch (error) {
        console.error('❌ [auth_controller] Login error:', error);
        console.error('📚 [auth_controller] Error stack:', error.stack);
        res.status(500).json({ message: 'Internal server error' });
    }
};

const forgotPassword = async (req, res) => {
    console.log('🔑 [auth_controller] Forgot password request received');
    console.log('📋 [auth_controller] Request body:', { 
        email: req.body.email ? '***' : 'missing'
    });
    
    try {
        const { email } = req.body;

        // Validation
        if (!email) {
            console.log('❌ [auth_controller] Forgot password failed - email missing');
            return res.status(400).json({
                message: 'Email is required'
            });
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            console.log('❌ [auth_controller] Forgot password failed - invalid email format');
            return res.status(400).json({
                message: 'Invalid email format'
            });
        }
        console.log('✅ [auth_controller] Email format validation passed');

        // Find user by email
        console.log('🔍 [auth_controller] Searching for user by email...');
        const [users] = await mysqlConnection.execute(`
            SELECT u.id, u.name, u.username, u.email, u.is_email_verified, u.isActive, u.is_blocked, u.is_deleted, ur.role_id
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            WHERE u.email = ? AND u.is_deleted = 0
        `, [email]);

        if (users.length === 0) {
            console.log('❌ [auth_controller] Forgot password failed - email not registered');
            return res.status(404).json({
                message: 'This email is not registered'
            });
        }
        console.log(`✅ [auth_controller] User found with ID: ${users[0].id}`);

        const user = users[0];

        // Check if user is blocked
        if (user.is_blocked) {
            return res.status(403).json({
                message: 'Account is blocked. Please contact support.'
            });
        }

        // Check if email is verified
        if (!user.is_email_verified) {
            return res.status(403).json({
                message: 'Email is not verified. Please verify your email first.'
            });
        }

        // Check if account is active
        if (!user.isActive) {
            return res.status(403).json({
                message: 'Account is deactivated. Please contact support.'
            });
        }

        // Generate unique OTP for password reset
        const otpCode = await generateUniqueOtp();
        const otpExpireAt = new Date(Date.now() + (parseInt(OTP_EXPIRE_TIME) * 60 * 1000)); // OTP_EXPIRE_TIME minutes from now

        // Update user with OTP for password reset
        await mysqlConnection.execute(`
            UPDATE users 
            SET otp_code = ?, otp_expire_at = ? 
            WHERE id = ?
        `, [otpCode, otpExpireAt, user.id]);

        // Send forgot password OTP email
        const emailResult = await sendForgotPasswordOtpEmail(email, otpCode, user.name);

        // Return success response
        res.status(200).json({
            message: 'Password reset OTP sent to your email.',
            second_message: `OTP valid for ${OTP_EXPIRE_TIME} minutes`,
            user: {
                id: user.id,
                name: user.name,
                username: user.username,
                email: user.email,
                role_id: user.role_id
            },
            otp: otpCode, // Remove this in production
            otpExpireAt,
            otpExpireTime: `${OTP_EXPIRE_TIME} minutes`,
            email_sent: emailResult.success
        });

    } catch (error) {
        console.error('Error in forgotPassword:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

const verifyForgotPasswordOtp = async (req, res) => {
    try {
        const { email, otp, newPassword } = req.body;

        // Validation
        if (!email || !otp || !newPassword) {
            return res.status(400).json({
                message: 'Email, OTP, and new password are required'
            });
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                message: 'Invalid email format'
            });
        }

        // Password strength validation (minimum 6 characters)
        if (newPassword.length < 6) {
            return res.status(400).json({
                message: 'Password must be at least 8 characters long'
            });
        }

        // Find user with matching email and OTP
        const [users] = await mysqlConnection.execute(`
            SELECT u.id, u.name, u.username, u.email, u.otp_code, u.otp_expire_at, u.is_email_verified, u.isActive, u.is_blocked, u.is_deleted, ur.role_id
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            WHERE u.email = ? AND u.otp_code = ? AND u.is_deleted = 0
        `, [email, otp]);

        if (users.length === 0) {
            return res.status(400).json({
                message: 'Invalid OTP or email'
            });
        }

        const user = users[0];

        // Check if user is blocked
        if (user.is_blocked) {
            return res.status(403).json({
                message: 'Account is blocked. Please contact support.'
            });
        }

        // Check if email is verified
        if (!user.is_email_verified) {
            return res.status(403).json({
                message: 'Email is not verified. Please verify your email first.'
            });
        }

        // Check if account is active
        if (!user.isActive) {
            return res.status(403).json({
                message: 'Account is deactivated. Please contact support.'
            });
        }

        // Check if OTP is expired
        const now = new Date();
        const otpExpireAt = new Date(user.otp_expire_at);

        if (now > otpExpireAt) {
            return res.status(400).json({
                message: 'OTP expired. Please request a new one.'
            });
        }

        // Hash new password
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // Update password and clear OTP
        await mysqlConnection.execute(`
            UPDATE users 
            SET password = ?, otp_code = NULL, otp_expire_at = NULL 
            WHERE id = ?
        `, [hashedPassword, user.id]);

        // Generate JWT token for auto-login after password reset
        const token = await generateToken(user);

        res.status(200).json({
            message: 'Password reset successful. You can now login with your new password.',
            token: token,
            authExpireTime: JWT_EXPIRE_TIME,
            user: {
                id: user.id,
                name: user.name,
                username: user.username,
                email: user.email,
                is_email_verified: true,
                role_id: user.role_id
            }
        });

    } catch (error) {
        console.error('Error in verifyForgotPasswordOtp:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Regenerate registration OTP (resend OTP if user not verified)
const regenerateOTP = async (req, res) => {
    try {
        const { email } = req.body;

        // Validation
        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({ message: 'Invalid email format' });
        }

        // Find user by email (not deleted)
        const [users] = await mysqlConnection.execute(`
            SELECT u.id, u.name, u.username, u.email, u.is_email_verified, u.is_blocked, u.is_deleted, ur.role_id
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            WHERE u.email = ? AND u.is_deleted = 0
        `, [email]);

        if (users.length === 0) {
            return res.status(404).json({ message: 'This email is not registered' });
        }

        const user = users[0];

        // Blocked account check
        if (user.is_blocked) {
            return res.status(403).json({ message: 'Account is blocked. Please contact support.' });
        }

        // Already verified
        if (user.is_email_verified) {
            return res.status(400).json({ message: 'Email already verified' });
        }

        // Generate new unique OTP and expiry
        const otpCode = await generateUniqueOtp();
        const otpExpireAt = new Date(Date.now() + (parseInt(OTP_EXPIRE_TIME) * 60 * 1000));

        // Save OTP
        await mysqlConnection.execute(`
            UPDATE users SET otp_code = ?, otp_expire_at = ? WHERE id = ?
        `, [otpCode, otpExpireAt, user.id]);

        // Send OTP email
        const emailResult = await sendRegisterOtpEmail(email, otpCode, user.name);

        // Respond
        return res.status(200).json({
            message: 'OTP regenerated and sent to your email.',
            second_message: `OTP valid for ${OTP_EXPIRE_TIME} minutes`,
            user: {
                id: user.id,
                name: user.name,
                username: user.username,
                email: user.email,
                role_id: user.role_id
            },
            otp: otpCode, // Remove in production
            otpExpireAt,
            otpExpireTime: `${OTP_EXPIRE_TIME} minutes`,
            email_sent: emailResult.success
        });

    } catch (error) {
        console.error('Error in regenerateOTP:', error);
        return res.status(500).json({ message: 'Internal server error' });
    }
};

const logout = async (req, res) => {
    try {
        const { email } = req.body;

        // Validation
        if (!email) {
            return res.status(400).json({
                message: 'Email is required'
            });
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                message: 'Invalid email format'
            });
        }

        // Find user by email
        const [users] = await mysqlConnection.execute(`
            SELECT u.id, u.name, u.username, u.email, u.isActive, u.is_blocked, u.is_deleted, ur.role_id
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            WHERE u.email = ? AND u.is_deleted = 0
        `, [email]);

        if (users.length === 0) {
            return res.status(404).json({
                message: 'User not found'
            });
        }

        const user = users[0];

        // Check if user is blocked or deleted
        if (user.is_blocked || user.is_deleted || !user.isActive) {
            return res.status(403).json({
                message: 'Account is not active'
            });
        }

        // In a real application, you might want to:
        // 1. Invalidate JWT tokens
        // 2. Clear session data
        // 3. Log the logout activity
        // 4. Update last logout timestamp

        // For now, we'll just return a success response
        // You can extend this based on your authentication strategy

        res.status(200).json({
            message: 'Logout successful',
            user: {
                id: user.id,
                name: user.name,
                username: user.username,
                email: user.email,
                role_id: user.role_id
            }
        });

    } catch (error) {
        console.error('Error in logout:', error);
        res.status(500).json({ message: 'Internal server error' });
  }
};

// Debug endpoint to check user OTP status (remove in production)
const debugUserOtp = async (req, res) => {
    try {
        const { email } = req.query;

        if (!email) {
            return res.status(400).json({ message: 'Email parameter required' });
        }

        const [users] = await mysqlConnection.execute(`
            SELECT id, email, otp_code, otp_expire_at, is_email_verified, isActive
            FROM users
            WHERE email = ? AND is_deleted = 0
        `, [email.trim().toLowerCase()]);

        if (users.length === 0) {
            return res.status(404).json({ message: 'User not found' });
        }

        const user = users[0];
        const now = new Date();
        const otpExpiry = new Date(user.otp_expire_at);

        res.json({
            user_id: user.id,
            email: user.email,
            otp_code: user.otp_code,
            otp_expire_at: user.otp_expire_at,
            otp_expired: now > otpExpiry,
            is_email_verified: user.is_email_verified,
            isActive: user.isActive,
            current_time: now.toISOString()
        });
    } catch (error) {
        console.error('Debug OTP error:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

console.log('✅ [auth_controller] Authentication controller initialized successfully');

module.exports = {
    register,
    verifyRegisterOtp,
    login,
    logout,
    forgotPassword,
    verifyForgotPasswordOtp,
    regenerateOTP,
    debugUserOtp // Remove in production
};
